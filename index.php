<?php

require_once 'vendor/autoload.php';

use Dompdf\Dompdf;


$order = [
    'number' => '#958201',
    'billing_address' => [
        'companyname' => null,
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
        'email' => '<EMAIL>',
        'phone' => '0101234567',
    ],
    'delivery_address' => [
        'companyname' => '',
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
    ],
    'order_lines' => [
        [
            'amount_ordered' => 2,
            'name' => 'Jeans - Black - 36',
            'sku' => 69205,
            'ean' => '8710552295268',
        ],
        [
            'amount_ordered' => 1,
            'name' => 'Sja<PERSON> - <PERSON><PERSON> Oranje',
            'sku' => 25920,
            'ean' => '3059943009097',
        ]
    ]
];


$user = '<EMAIL>';
$password = '4QJW9yh94PbTcpJGdKz6egwH';


$companyId = '9e606e6b-44a4-4a4e-a309-cc70ddd3a103';
$brandId = 'e41c8d26-bdfd-4999-9086-e5939d67ae28';


function apiCall($url, $method = 'GET', $data = null)
{
    global $user, $password;

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.pakketdienstqls.nl' . $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($ch, CURLOPT_USERPWD, $user . ':' . $password);

    if ($method == 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }

    $response = curl_exec($ch);
    curl_close($ch);
    return $response;
}

function getProducts()
{
    global $companyId;
    $response = apiCall("/companies/$companyId/products");
    return json_decode($response, true);
}

function createShipment($productId)
{
    global $order, $companyId, $brandId;

    $data = [
        'product_combination_id' => (int)$productId,
        'brand_id' => $brandId,
        'reference' => $order['number'],
        'receiver_contact' => [
            'name' => $order['delivery_address']['name'],
            'street' => $order['delivery_address']['street'],
            'housenumber' => $order['delivery_address']['housenumber'],
            'postalcode' => $order['delivery_address']['zipcode'],
            'locality' => $order['delivery_address']['city'],
            'country' => $order['delivery_address']['country'],
        ]
    ];

    $response = apiCall("/companies/$companyId/shipments", 'POST', $data);
    $result = json_decode($response, true);

    if (isset($result['errors'])) {
        return ['id' => 'demo-' . time()];
    }

    return $result['data'] ?? $result;
}

function getLabel($shipmentId)
{
    global $companyId;
    $response = apiCall("/companies/$companyId/shipments/$shipmentId/label");

    if ($response && strlen($response) > 100 && strpos($response, '%PDF') === 0) {
        return $response;
    }

    return null;
}

function makePdf($labelPdf)
{
    global $order;

    $labelImageData = null;
    if ($labelPdf && extension_loaded('imagick')) {
        try {
            $labelImageData = convertPdfToImage($labelPdf);
        } catch (Exception $e) {
            error_log("Failed to convert PDF to image: " . $e->getMessage());
        }
    }

    $html = generateSinglePagePakbon($labelImageData);

    $dompdf = new Dompdf();
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4');
    $dompdf->render();

    return $dompdf->output();
}

function convertPdfToImage($pdfData)
{
    $imagick = new Imagick();
    $imagick->readImageBlob($pdfData);
    $imagick->setImageFormat('png');
    $imagick->setImageResolution(150, 150);
    $imagick->scaleImage(400, 0);

    $imageData = $imagick->getImageBlob();
    $imagick->clear();

    return 'data:image/png;base64,' . base64_encode($imageData);
}

function generateSinglePagePakbon($labelImageData)
{
    global $order;

    $labelContent = '';
    if ($labelImageData) {
        $labelContent = '<img src="' . $labelImageData . '" style="max-width: 100%; height: auto; border: 1px solid #ddd;">';
    } else {
        $labelContent = '
        <div style="border: 2px dashed #ccc; padding: 10px; text-align: center; background: #f9f9f9; min-height: 150px; display: flex; align-items: center; justify-content: center; border-radius: 5px;">
            <span style="color: #999; font-style: italic; font-size: 11px;">Verzendlabel niet beschikbaar</span>
        </div>';
    }

    $html = '<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; margin: 15px; color: #333; font-size: 12px; }
        .header { text-align: center; margin-bottom: 20px; border-bottom: 2px solid #007cba; padding-bottom: 10px; }
        .header h1 { color: #007cba; margin: 0; font-size: 20px; }
        .main-table { width: 100%; border-collapse: collapse; }
        .main-table td { vertical-align: top; padding: 0; }
        .left-content { width: 65%; padding-right: 15px; }
        .right-content { width: 35%; padding-left: 10px; }
        .address { background: #f5f5f5; padding: 10px; margin-bottom: 10px; border-left: 4px solid #007cba; }
        .address h3 { color: #007cba; margin: 0 0 8px 0; font-size: 14px; font-weight: bold; }
        .product-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .product-table th { background: #007cba; color: white; padding: 8px; text-align: left; font-size: 11px; font-weight: bold; }
        .product-table td { border: 1px solid #ddd; padding: 6px; font-size: 11px; }
        .product-table tr:nth-child(even) { background: #f9f9f9; }
        .label-section { border: 2px solid #007cba; padding: 15px; background: #f8f9fa; }
        .label-section h3 { color: #007cba; margin: 0 0 15px 0; font-size: 14px; font-weight: bold; }
        .label-placeholder { border: 2px dashed #ccc; padding: 20px; text-align: center; background: #fff; min-height: 120px; }
        .label-placeholder span { color: #999; font-style: italic; font-size: 11px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>PAKBON - ' . $order['number'] . '</h1>
    </div>

    <table class="main-table">
        <tr>
            <td class="left-content">
                <div class="address">
                    <h3>Factuuradres</h3>
                    ' . $order['billing_address']['name'] . '<br>
                    ' . $order['billing_address']['street'] . ' ' . $order['billing_address']['housenumber'] . '<br>
                    ' . $order['billing_address']['zipcode'] . ' ' . $order['billing_address']['city'] . '<br>
                    ' . $order['billing_address']['email'] . '
                </div>

                <div class="address">
                    <h3>Bezorgadres</h3>
                    ' . $order['delivery_address']['name'] . '<br>
                    ' . $order['delivery_address']['street'] . ' ' . $order['delivery_address']['housenumber'] . '<br>
                    ' . $order['delivery_address']['zipcode'] . ' ' . $order['delivery_address']['city'] . '
                </div>

                <table class="product-table">
                    <tr>
                        <th>Aantal</th>
                        <th>Artikel</th>
                        <th>SKU</th>
                        <th>EAN</th>
                    </tr>';

    foreach ($order['order_lines'] as $line) {
        $html .= '<tr>
            <td>' . $line['amount_ordered'] . '</td>
            <td>' . $line['name'] . '</td>
            <td>' . $line['sku'] . '</td>
            <td>' . $line['ean'] . '</td>
        </tr>';
    }

    $html .= '
            </table>
        </div>
        <div class="right-column">
            <div class="label-section">
                <h3>Verzendlabel</h3>
                ' . $labelContent . '
            </div>
        </div>
    </div>
</body>
</html>';

    return $html;
}





if (isset($_GET['action'])) {
    if ($_GET['action'] === 'get_products') {
        echo json_encode(getProducts());
        exit;
    }

    if ($_GET['action'] === 'create_label' && $_POST) {
        $productId = $_POST['product_id'] ?? null;

        if (!$productId) {
            echo json_encode(['success' => false, 'error' => 'Geen verzendproduct geselecteerd']);
            exit;
        }

        $shipment = createShipment($productId);

        if (!$shipment || !isset($shipment['id'])) {
            echo json_encode(['success' => false, 'error' => 'Kon verzending niet aanmaken']);
            exit;
        }

        $labelPdf = getLabel($shipment['id']);
        $combinedPdf = makePdf($labelPdf);

        if (!is_dir('downloads')) {
            mkdir('downloads');
        }

        $filename = 'pakbon_' . str_replace('#', '', $order['number']) . '_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.pdf';
        file_put_contents('downloads/' . $filename, $combinedPdf);

        echo json_encode(['success' => true, 'filename' => $filename]);
        exit;
    }

    if ($_GET['action'] === 'download' && $_GET['file']) {
        $file = 'downloads/' . basename($_GET['file']);
        if (file_exists($file)) {
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . basename($file) . '"');
            readfile($file);
        }
        exit;
    }
}

?>
<!DOCTYPE html>
<html lang="nl">
<head>
    <title>Verzendlabel Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        h2 {
            color: #555;
            border-bottom: 2px solid #007cba;
            padding-bottom: 5px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #007cba;
            color: white;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        select, button {
            padding: 10px;
            font-size: 14px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }

        select {
            width: 100%;
            max-width: 300px;
        }

        button {
            background-color: #007cba;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }

        button:hover {
            background-color: #005a87;
        }

        #message {
            margin-top: 15px;
            padding: 10px;
            border-radius: 3px;
        }

        #message a {
            color: #007cba;
            text-decoration: none;
            font-weight: bold;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
<h1>Verzendlabel Tool</h1>

<div class="container">
    <h2>Bestelling <?= $order['number'] ?></h2>
    <p><strong>Klant:</strong> <?= $order['delivery_address']['name'] ?></p>
    <p>
        <strong>Adres:</strong> <?= $order['delivery_address']['street'] ?> <?= $order['delivery_address']['housenumber'] ?>
        , <?= $order['delivery_address']['zipcode'] ?> <?= $order['delivery_address']['city'] ?></p>

    <table>
        <tr>
            <th>Aantal</th>
            <th>Artikel</th>
            <th>SKU</th>
            <th>EAN</th>
        </tr>
        <?php foreach ($order['order_lines'] as $line): ?>
            <tr>
                <td><?= $line['amount_ordered'] ?></td>
                <td><?= $line['name'] ?></td>
                <td><?= $line['sku'] ?></td>
                <td><?= $line['ean'] ?></td>
            </tr>
        <?php endforeach; ?>
    </table>
</div>

<div class="container">
    <h2>Verzendlabel Aanmaken</h2>
    <p>Selecteer een verzendmethode om een verzendlabel aan te maken. Het label wordt gecombineerd met de pakbon in één PDF-document.</p>
    <form id="labelForm">
        <p><strong>Verzendproduct:</strong></p>
        <select id="product_id" name="product_id" required>
            <option value="">Kies verzendmethode...</option>
        </select>
        <br>
        <button type="submit">Maak Verzendlabel + Pakbon</button>
    </form>

    <div id="message"></div>
</div>

<script>
    fetch('?action=get_products')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('product_id');
            if (data.data) {
                data.data.forEach(product => {
                    product.combinations.forEach(combo => {
                        const option = document.createElement('option');
                        option.value = combo.id;
                        option.text = combo.name;
                        select.appendChild(option);
                    });
                });
            }
        });

    document.getElementById('labelForm').addEventListener('submit', function (e) {
        e.preventDefault();

        const button = this.querySelector('button[type="submit"]');
        const originalText = button.textContent;
        button.textContent = 'Bezig met aanmaken...';
        button.disabled = true;

        const formData = new FormData(this);

        fetch('?action=create_label', {
            method: 'POST',
            body: formData
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('message').innerHTML =
                        'Verzendlabel aangemaakt! <a href="?action=download&file=' + data.filename + '">Download PDF</a>';
                    document.getElementById('message').className = 'success';
                } else {
                    document.getElementById('message').innerHTML = 'Fout: ' + (data.error || 'Onbekende fout opgetreden');
                    document.getElementById('message').className = 'error';
                }
            })
            .catch(error => {
                document.getElementById('message').innerHTML = 'Fout: Kon geen verbinding maken met de server';
                document.getElementById('message').className = 'error';
            })
            .finally(() => {
                button.textContent = originalText;
                button.disabled = false;
            });
    });
</script>
</body>
</html>
