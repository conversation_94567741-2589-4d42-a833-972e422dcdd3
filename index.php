<?php
require_once 'vendor/autoload.php';

use Dompdf\Dompdf;

$qls_user = '<EMAIL>';
$qls_pass = '4QJW9yh94PbTcpJGdKz6egwH';
$company_id = '9e606e6b-44a4-4a4e-a309-cc70ddd3a103';
$brand_id = 'e41c8d26-bdfd-4999-9086-e5939d67ae28';

$order = [
    'number' => '#958201',
    'billing_address' => [
        'name' => 'John Doe',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'email' => '<EMAIL>',
    ],
    'delivery_address' => [
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
    ],
    'order_lines' => [
        ['amount_ordered' => 2, 'name' => 'Jeans - Black - 36', 'sku' => 69205, 'ean' => '8710552295268'],
        ['amount_ordered' => 1, 'name' => 'Sjaal - Rood Oranje', 'sku' => 25920, 'ean' => '3059943009097']
    ]
];

function apiCall($url, $method = 'GET', $data = null)
{
    global $user, $password;

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.pakketdienstqls.nl' . $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($ch, CURLOPT_USERPWD, $user . ':' . $password);

    if ($method == 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    // Basic error check - could be improved
    if ($httpCode >= 400) {
        error_log("API call failed: $url - HTTP $httpCode");
        return false;
    }

    return $response;
}

function getProducts()
{
    global $companyId;
    $response = apiCall("/companies/$companyId/products");
    if (!$response) return null;
    return json_decode($response, true);
}

function createShipment($productId)
{
    global $order, $companyId, $brandId;

    $data = [
        'product_combination_id' => (int)$productId,
        'brand_id' => $brandId,
        'reference' => $order['number'],
        'receiver_contact' => [
            'name' => $order['delivery_address']['name'],
            'street' => $order['delivery_address']['street'],
            'housenumber' => $order['delivery_address']['housenumber'],
            'postalcode' => $order['delivery_address']['zipcode'],
            'locality' => $order['delivery_address']['city'],
            'country' => $order['delivery_address']['country'],
        ]
    ];

    $response = apiCall("/companies/$companyId/shipments", 'POST', $data);
    if (!$response) return null;

    $result = json_decode($response, true);

    // Fallback for demo purposes when API fails
    if (isset($result['errors']) || !$result) {
        return ['id' => 'demo-' . time()];
    }

    return $result['data'] ?? $result;
}

function getLabel($shipmentId)
{
    global $order;

    // For now just generate a mock label since real API might not work
    // TODO: implement actual PDF label retrieval from QLS API
    $labelHtml = '
    <div style="border: 2px solid #000; padding: 15px; background: white; font-family: Arial; font-size: 10px; width: 300px; height: 200px;">
        <div style="text-align: center; font-weight: bold; font-size: 12px; margin-bottom: 10px;">QLS VERZENDLABEL</div>
        <div style="margin-bottom: 8px;"><strong>Zending:</strong> ' . $shipmentId . '</div>
        <div style="margin-bottom: 8px;">
            <strong>Naar:</strong><br>
            ' . $order['delivery_address']['name'] . '<br>
            ' . $order['delivery_address']['street'] . ' ' . $order['delivery_address']['housenumber'] . '<br>
            ' . $order['delivery_address']['zipcode'] . ' ' . $order['delivery_address']['city'] . '
        </div>
        <div style="text-align: center; margin-top: 15px; padding: 10px; background: #f0f0f0;">
            <div style="font-family: monospace; font-size: 14px; font-weight: bold;">|||| ||| |||| ||||</div>
            <div style="font-size: 8px; margin-top: 5px;">Track & Trace: QLS' . substr($shipmentId, -8) . '</div>
        </div>
    </div>';

    return $labelHtml;
}

function makePdf($labelHtml)
{
    global $order;

    $html = '<!DOCTYPE html>
<html lang="nl">
<head>
    <style>
        /* Basic styling for the PDF layout */
        body { font-family: Arial; margin: 15px; font-size: 12px; }
        .header { text-align: center; margin-bottom: 20px; border-bottom: 2px solid #007cba; padding-bottom: 10px; }
        .header h1 { color: #007cba; margin: 0; font-size: 20px; }
        .addresses { width: 100%; margin-bottom: 15px; }
        .address-left { float: left; width: 48%; }
        .address-right { float: right; width: 48%; }
        .address { background: #f5f5f5; padding: 10px; margin-bottom: 10px; border-left: 4px solid #007cba; }
        .address h3 { color: #007cba; margin: 0 0 8px 0; font-size: 14px; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th { background: #007cba; color: white; padding: 8px; text-align: left; font-size: 11px; }
        td { border: 1px solid #ddd; padding: 6px; font-size: 11px; }
        tr:nth-child(even) { background: #f9f9f9; }
        .label-section { border: 2px solid #007cba; padding: 15px; background: #f8f9fa; margin-top: 20px; clear: both; }
        .label-section h3 { color: #007cba; margin: 0 0 15px 0; font-size: 14px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>PAKBON - ' . $order['number'] . '</h1>
    </div>
    
    <div class="addresses">
        <div class="address-left">
            <div class="address">
                <h3>Factuuradres</h3>
                ' . $order['billing_address']['name'] . '<br>
                ' . $order['billing_address']['street'] . ' ' . $order['billing_address']['housenumber'] . '<br>
                ' . $order['billing_address']['zipcode'] . ' ' . $order['billing_address']['city'] . '<br>
                ' . $order['billing_address']['email'] . '
            </div>
        </div>
        <div class="address-right">
            <div class="address">
                <h3>Bezorgadres</h3>
                ' . $order['delivery_address']['name'] . '<br>
                ' . $order['delivery_address']['street'] . ' ' . $order['delivery_address']['housenumber'] . '<br>
                ' . $order['delivery_address']['zipcode'] . ' ' . $order['delivery_address']['city'] . '
            </div>
        </div>
    </div>
    
    <table class="product-table">
        <tr>
            <th>Aantal</th>
            <th>Artikel</th>
            <th>SKU</th>
            <th>EAN</th>
        </tr>';

    foreach ($order['order_lines'] as $line) {
        $html .= '<tr>
            <td>' . $line['amount_ordered'] . '</td>
            <td>' . $line['name'] . '</td>
            <td>' . $line['sku'] . '</td>
            <td>' . $line['ean'] . '</td>
        </tr>';
    }

    $html .= '
    </table>
    
    <div class="label-section">
        <h3>Verzendlabel</h3>
        ' . $labelHtml . '
    </div>
</body>
</html>';

    $dompdf = new Dompdf();
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4');
    $dompdf->render();

    return $dompdf->output();
}

if (isset($_GET['action'])) {
    if ($_GET['action'] === 'get_products') {
        echo json_encode(getProducts());
        exit;
    }

    if ($_GET['action'] === 'create_label' && $_POST) {
        $productId = $_POST['product_id'] ?? null;

        if (!$productId) {
            echo json_encode(['success' => false, 'error' => 'Geen verzendproduct geselecteerd']);
            exit;
        }

        $shipment = createShipment($productId);

        if (!$shipment || !isset($shipment['id'])) {
            echo json_encode(['success' => false, 'error' => 'Kon verzending niet aanmaken']);
            exit;
        }

        $labelHtml = getLabel($shipment['id']);
        $pdf = makePdf($labelHtml);

        // Make sure downloads dir exists
        if (!is_dir('downloads')) {
            mkdir('downloads', 0755, true);
        }

        $filename = 'pakbon_' . str_replace('#', '', $order['number']) . '_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.pdf';
        $success = file_put_contents('downloads/' . $filename, $pdf);

        if (!$success) {
            echo json_encode(['success' => false, 'error' => 'Kon PDF niet opslaan']);
            exit;
        }

        echo json_encode(['success' => true, 'filename' => $filename]);
        exit;
    }

    if ($_GET['action'] === 'download' && $_GET['file']) {
        $file = 'downloads/' . basename($_GET['file']);
        if (file_exists($file)) {
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . basename($file) . '"');
            readfile($file);
        }
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="nl">
<head>
    <title>Verzendlabel Tool</title>
    <style>
        body { font-family: Arial; max-width: 800px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 20px; margin-bottom: 20px; }
        h1 { text-align: center; margin-bottom: 30px; }
        h2 { color: #555; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #007cba; color: white; }
        tr:nth-child(even) { background: #f9f9f9; }
        select, button { padding: 10px; font-size: 14px; border: 1px solid #ddd; }
        select { width: 100%; max-width: 300px; }

        button { background: #007cba; color: white; border: none; cursor: pointer; margin-top: 10px; }
        button:hover { background: #005a87; }
        #message { margin-top: 15px; padding: 10px; }
        #message a { color: #007cba; text-decoration: none; font-weight: bold; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
<h1>Verzendlabel Tool</h1>

<div class="container">
    <h2>Bestelling <?= $order['number'] ?></h2>
    <p><strong>Klant:</strong> <?= $order['delivery_address']['name'] ?></p>
    <p>
        <strong>Adres:</strong> <?= $order['delivery_address']['street'] ?> <?= $order['delivery_address']['housenumber'] ?>
        , <?= $order['delivery_address']['zipcode'] ?> <?= $order['delivery_address']['city'] ?></p>

    <table>
        <tr>
            <th>Aantal</th>
            <th>Artikel</th>
            <th>SKU</th>
            <th>EAN</th>
        </tr>
        <?php foreach ($order['order_lines'] as $line): ?>
            <tr>
                <td><?= $line['amount_ordered'] ?></td>
                <td><?= $line['name'] ?></td>
                <td><?= $line['sku'] ?></td>
                <td><?= $line['ean'] ?></td>
            </tr>
        <?php endforeach; ?>
    </table>
</div>

<div class="container">
    <h2>Verzendlabel Aanmaken</h2>
    <p>Selecteer een verzendmethode om een verzendlabel aan te maken. Het label wordt gecombineerd met de pakbon in één
        PDF-document.</p>
    <form id="labelForm">
        <strong><label for="product_id">Verzendproduct:</label></strong>
        <select id="product_id" name="product_id" required>
            <option value="">Kies verzendmethode...</option>
        </select>
        <br>
        <button type="submit">Maak Verzendlabel + Pakbon</button>
    </form>

    <div id="message"></div>
</div>

<script>
    fetch('?action=get_products')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('product_id');
            if (data.data) {
                data.data.forEach(product => {
                    product.combinations.forEach(combo => {
                        const option = document.createElement('option');
                        option.value = combo.id;
                        option.text = combo.name;
                        select.appendChild(option);
                    });
                });
            }
        });

    document.getElementById('labelForm').addEventListener('submit', function (e) {
        e.preventDefault();

        const button = this.querySelector('button[type="submit"]');
        const originalText = button.textContent;
        button.textContent = 'Bezig met aanmaken...';
        button.disabled = true;

        const formData = new FormData(this);

        fetch('?action=create_label', {
            method: 'POST',
            body: formData
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('message').innerHTML =
                        'Verzendlabel aangemaakt! <a href="?action=download&file=' + data.filename + '">Download PDF</a>';
                    document.getElementById('message').className = 'success';
                } else {
                    document.getElementById('message').innerHTML = 'Fout: ' + (data.error || 'Onbekende fout opgetreden');
                    document.getElementById('message').className = 'error';
                }
            })
            .catch(() => {
                document.getElementById('message').innerHTML = 'Fout: Kon geen verbinding maken met de server';
                document.getElementById('message').className = 'error';
            })
            .finally(() => {
                button.textContent = originalText;
                button.disabled = false;
            });
    });
</script>
</body>
</html>
